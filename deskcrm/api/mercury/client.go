package mercury

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/helpers"
	"deskcrm/libs/utils"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Mercury,
	}
	return c
}

const (
	UriObjectGetApi   = "/mercury/object/get"
	DefaultExpireTime = 60 * 5
	ConfigCacheKeyPre = "deskcrm_mercury_"
)

func (c *Client) GetConfigForString(ctx *gin.Context, key string, ttl int64) (string, error) {
	configInfo, err := c.getConfigFromCache(ctx, key)
	if err != nil || len(configInfo.Val) == 0 {
		configInfo, err = c.getMercury(ctx, key)
		if err = c.setConfigCache(ctx, configInfo, key, ttl); err != nil {
			zlog.Warnf(ctx, "set mercury config cache failed, key: %s, config: %+v ", key, configInfo)
		}
	}

	return configInfo.Val, nil
}

func (c *Client) getMercury(ctx *gin.Context, key string) (resp *ConfigInfo, err error) {
	resp = &ConfigInfo{}

	req := map[string]interface{}{
		"key": key,
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)
	res, err := c.cli.HttpPost(ctx, UriObjectGetApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetConfigForJson(ctx *gin.Context, key string, ttl int64, val interface{}) error {
	valType := reflect.ValueOf(val).Type()
	if valType.Kind() != reflect.Ptr {
		return errors.New(fmt.Sprintf("require ptr to val, got %s", valType.Kind()))
	}

	configInfo, err := c.GetConfigForString(ctx, key, ttl)
	if err != nil {
		return err
	}

	if err = json.Unmarshal([]byte(configInfo), val); err != nil {
		return err
	}

	return nil
}

func (c *Client) setConfigCache(ctx *gin.Context, configInfo *ConfigInfo, key string, ttl int64) error {
	cacheKey := c.getCacheKey(key)
	if ttl == 0 {
		ttl = DefaultExpireTime
	}
	return helpers.RedisClient.Set(ctx, cacheKey, configInfo.Val, ttl)
}

func (c *Client) getConfigFromCache(ctx *gin.Context, key string) (*ConfigInfo, error) {
	cacheKey := c.getCacheKey(key)
	data, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	ret := &ConfigInfo{Val: string(data)}
	return ret, nil
}

func (c *Client) getCacheKey(key string) string {
	return ConfigCacheKeyPre + key
}

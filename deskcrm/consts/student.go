package consts

// 访谈记录状态常量
const (
	StudentInterviewStatusOK      = 0 // 未删除
	StudentInterviewStatusDeleted = 1 // 已删除
)

// 访谈类型常量
const (
	StudentInterviewNoType      = 0  // 未分类
	StudentInterviewDaily       = 1  // 日常
	StudentInterviewNewUser     = 2  // 用户家访
	StudentInterviewEnroll      = 3  // 续报
	StudentInterviewWithdraw    = 4  // 退课
	StudentInterviewNuCallback  = 5  // 新用户回访
	StudentInterviewBcInterview = 6  // 课中回访
	StudentInterviewOther       = 99 // 其他
	StudentInterviewTftcJsq     = 7  // 金丝雀投放透传
	StudentInterviewSpaceExpand = 8  // 扩科
)

// 访谈记录查看类型常量
const (
	StudentInterviewViewAll    = 1 // 查看全部
	StudentInterviewViewCourse = 2 // 只看该课程
	StudentInterviewViewSelf   = 3 // 只看自己
)

// AI访谈记录类型常量
const (
	StudentInterviewAIViewAll  = 0 // AI查看全部
	StudentInterviewAIViewSelf = 2 // AI只看自己
)

// 访谈记录类型常量
const (
	StudentInterviewRecordTypeManual = 1 // 手动记录
	StudentInterviewRecordTypeAI     = 2 // AI记录
)

// 访谈记录来源类型常量
const (
	ServicePageDeskStudentInterviewOriginDesk = 1 // DESK来源
	ServicePageDeskStudentInterviewOriginApi  = 2 // API来源
)

// 渠道类型常量
const (
	StudentInterviewChannelTypePhone  = 1 // 手机
	StudentInterviewChannelTypeWechat = 2 // 微信
)

// 访谈记录二进制映射的十进制数
const (
	StudentInterviewDailyValue       = 1   // 二进制第0位 01  =>1 * 2^0
	StudentInterviewNewUserValue     = 2   // 二进制第1位 10  =>0 * 2^0 + 1* 2^1
	StudentInterviewEnrollValue      = 4   // 二进制第2位 100 =>0 * 2^0 + 0* 2^1 + 1*2^2
	StudentInterviewWithdrawValue    = 8   // 1000
	StudentInterviewNuCallbackValue  = 16  // 10000
	StudentInterviewBcInterviewValue = 32  // 100000
	StudentInterviewOtherValue       = 64  // 1000000
	StudentInterviewTftcJsqValue     = 128 // 10000000
	StudentInterviewSpaceExpandValue = 256 // 100000000
)

// InterviewTypeValueMap 家访类型二进制位为1的十进制转换映射
var InterviewTypeValueMap = map[int]int{
	StudentInterviewDaily:       StudentInterviewDailyValue,
	StudentInterviewNewUser:     StudentInterviewNewUserValue,
	StudentInterviewEnroll:      StudentInterviewEnrollValue,
	StudentInterviewWithdraw:    StudentInterviewWithdrawValue,
	StudentInterviewNuCallback:  StudentInterviewNuCallbackValue,
	StudentInterviewBcInterview: StudentInterviewBcInterviewValue,
	StudentInterviewOther:       StudentInterviewOtherValue,
	StudentInterviewTftcJsq:     StudentInterviewTftcJsqValue,
	StudentInterviewSpaceExpand: StudentInterviewSpaceExpandValue,
}

// InterviewTypeNames 家访类型名称数组
var InterviewTypeNames = map[int]string{
	StudentInterviewNoType:      "未分类",
	StudentInterviewDaily:       "日常",
	StudentInterviewNewUser:     "用户家访",
	StudentInterviewEnroll:      "续报",
	StudentInterviewWithdraw:    "退课",
	StudentInterviewNuCallback:  "新用户回访",
	StudentInterviewBcInterview: "课中回访",
	StudentInterviewOther:       "其他",
	StudentInterviewTftcJsq:     "投放透传",
	StudentInterviewSpaceExpand: "扩科",
}

// ChannelTypeMap 渠道类型映射
var ChannelTypeMap = map[int]string{
	StudentInterviewChannelTypePhone:  "手机",
	StudentInterviewChannelTypeWechat: "微信",
}

// 关键行为分组ID常量
const (
	KeyBehaviorGroupIdTest     = 1 // 摸底测
	KeyBehaviorGroupIdSurvey   = 2 // 营销
	KeyBehaviorGroupIdAttend   = 3 // 到课
	KeyBehaviorGroupIdFinish   = 4 // 完课
	KeyBehaviorGroupIdPlayback = 5 // 回放
	KeyBehaviorGroupIdPreorder = 6 // 定金尾款已支付
)

// 关键行为分组名称映射
var KeyBehaviorGroupMap = map[int]string{
	KeyBehaviorGroupIdTest:     "摸底测",
	KeyBehaviorGroupIdSurvey:   "营销",
	KeyBehaviorGroupIdAttend:   "到课",
	KeyBehaviorGroupIdFinish:   "完课",
	KeyBehaviorGroupIdPlayback: "回放",
	KeyBehaviorGroupIdPreorder: "定金尾款已支付",
}

// 关键行为项目ID常量
const (
	KeyBehaviorItemIdPreComplites     = 1 // 摸底测
	KeyBehaviorItemIdNeedSurveyStatus = 2 // 挖需问卷
	KeyBehaviorItemIdOrderStatus      = 3 // 工作台预约问卷
	KeyBehaviorItemIdFnOrderStatus    = 4 // 蜂鸟预约问卷
	KeyBehaviorItemIdAttend           = 5 // 到课
	KeyBehaviorItemIdFinish           = 6 // 完课
	KeyBehaviorItemIdPlayback         = 7 // 回放
	KeyBehaviorItemIdPreorder         = 8 // 定金尾款已支付
)

// 年级分组常量
var (
	// 小学年级列表
	ElementaryGrades = []int64{1, 11, 12, 13, 14, 15, 16}
	// 初中年级列表
	MiddleGrades = []int64{20, 2, 3, 4}
	// 高中年级列表
	HighGrades = []int64{30, 5, 6, 7}
)

// 关键行为项目名称映射
var KeyBehaviorItemMap = map[int]string{
	KeyBehaviorItemIdPreComplites:     "摸底测",
	KeyBehaviorItemIdNeedSurveyStatus: "挖需问卷",
	KeyBehaviorItemIdOrderStatus:      "工作台预约问卷",
	KeyBehaviorItemIdFnOrderStatus:    "蜂鸟预约问卷",
	KeyBehaviorItemIdAttend:           "到课",
	KeyBehaviorItemIdFinish:           "完课",
	KeyBehaviorItemIdPlayback:         "回放",
	KeyBehaviorItemIdPreorder:         "定金尾款已支付",
}

// getDepartmentIdByGradeId 根据年级ID获取学段ID
func GetDepartmentIdByGradeId(gradeId int64) int {
	// 对应PHP版本的$GRADEMAPXB数组映射
	gradeMapXB := map[int64]int{
		1:   1,  // 小学
		11:  1,  // 一年级 -> 小学
		12:  1,  // 二年级 -> 小学
		13:  1,  // 三年级 -> 小学
		14:  1,  // 四年级 -> 小学
		15:  1,  // 五年级 -> 小学
		16:  1,  // 六年级 -> 小学
		2:   20, // 初一 -> 初中
		3:   20, // 初二 -> 初中
		4:   20, // 初三 -> 初中
		21:  20, // 预初 -> 初中
		20:  20, // 初中 -> 初中
		5:   30, // 高一 -> 高中
		6:   30, // 高二 -> 高中
		7:   30, // 高三 -> 高中
		30:  30, // 高中 -> 高中
		31:  30, // 职高一 -> 高中
		32:  30, // 职高二 -> 高中
		33:  30, // 职高三 -> 高中
		60:  60, // 学前 -> 学前
		61:  60, // 学前班 -> 学前
		62:  60, // 大班 -> 学前
		63:  60, // 中班 -> 学前
		64:  60, // 小班 -> 学前
		70:  70, // 成人 -> 成人
		71:  70, // 成人 -> 成人
		81:  80, // 大一 -> 大学
		82:  80, // 大二 -> 大学
		83:  80, // 大三 -> 大学
		84:  80, // 大四 -> 大学
		100: 80, // 研究生 -> 大学
		90:  90, // 低幼 -> 低幼
		91:  90, // 低幼 -> 低幼
		92:  30, // 高中衔接 -> 高中
	}

	// 获取学段ID
	departmentId, exists := gradeMapXB[gradeId]
	if !exists {
		// 默认返回初中ID，对应PHP版本的Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR = 20
		return 20
	}

	return departmentId
}

var Bed2Value = map[string]int{
	"":  0,
	"A": 1,
	"B": 2,
	"C": 3,
	"D": 4,
}

var UserBedTypeHover = map[string]string{
	"A": "A：学习积极性强；自觉性强；学习效果显著；沟通亲密度高",
	"B": "B：学习积极性较好；自觉性较好；学习效果一般；沟通亲密度中等",
	"C": "C：学习积极性较好；自觉性一般；学习效果一般；沟通亲密度一般",
	"D": "D：学习积极性一般；自觉性较弱；学习效果较差；沟通亲密度较差",
}

const PreClassHover = "课前分层仅关注日常沟通类数据即可，其他数据仅应用于课后模型"

const DifferenceDetailFunc = "GetUserDelaminationCommon"

var Categories = []string{
	CategoryCourseParticipation,
	CategoryPreClassPreparation,
	CategoryClassInteraction,
	CategoryAfterClassExercise,
	CategoryDailyCommunication,
	CategoryUnknown,
}

const (
	CategoryCourseParticipation = "课程参与"
	CategoryPreClassPreparation = "课前预习"
	CategoryClassInteraction    = "课中互动"
	CategoryAfterClassExercise  = "课后做题"
	CategoryDailyCommunication  = "日常沟通"
	CategoryUnknown             = "未知分类"
)

// 通话结果常量
const (
	CallResultConnected    = 1 // 接通
	CallResultNotConnected = 0 // 未接通
)

// 通话结果文本映射
var CallResultTextMap = map[int]string{
	CallResultConnected:    "接通",
	CallResultNotConnected: "未接通",
}

// 来源类型常量
const (
	SourceTypeCourse   = 1 // 课程
	SourceTypeMaintain = 2 // 维系
	SourceTypeOther    = 3 // 其他
)

// 通话记录来源类型常量 (对应PHP的Assistant_Ds_AssistantCallRecord)
const (
	SourceType16 = 16 // 挽单
)

// 来源类型名称映射
var SourceTypeNameMap = map[int]string{
	SourceTypeCourse:   "课程",
	SourceTypeMaintain: "维系",
	SourceTypeOther:    "其他",
}
